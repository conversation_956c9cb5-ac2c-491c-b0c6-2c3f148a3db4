// Cloudflare Worker for SPA routing
addEventListener('fetch', event => {
  event.respondWith(handleRequest(event.request))
})

async function handleRequest(request) {
  const url = new URL(request.url)
  const path = url.pathname
  
  // Handle static assets with proper MIME types
  if (/\.[a-z0-9]+$/i.test(path)) {
    const response = await fetch(request)

    // Clone the response to modify headers
    const newResponse = new Response(response.body, response)

    // Set correct MIME types for JavaScript modules
    if (path.endsWith('.js') || path.endsWith('.mjs')) {
      newResponse.headers.set('Content-Type', 'text/javascript; charset=utf-8')
    } else if (path.endsWith('.jsx') || path.endsWith('.tsx')) {
      newResponse.headers.set('Content-Type', 'text/javascript; charset=utf-8')
    } else if (path.endsWith('.css')) {
      newResponse.headers.set('Content-Type', 'text/css; charset=utf-8')
    } else if (path.endsWith('.json')) {
      newResponse.headers.set('Content-Type', 'application/json; charset=utf-8')
    } else if (path.endsWith('.html')) {
      newResponse.headers.set('Content-Type', 'text/html; charset=utf-8')
    }

    return newResponse
  }

  // For all other requests, return the main index.html
  const response = await fetch('https://your-pages-url.pages.dev/index.html')
  return new Response(response.body, {
    status: response.status,
    statusText: response.statusText,
    headers: {
      ...Object.fromEntries(response.headers.entries()),
      'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.google-analytics.com https://www.googletagmanager.com  https://umami.is https://cloud.umami.is https://bitdefender.com https://*.bitdefender.com https://static.cloudflareinsights.com https://posthog.com https://app.posthog.com https://us-assets.i.posthog.com https://*.teclara.tech https://teclara.tech; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com  https://umami.is https://cloud.umami.is https://bitdefender.com https://*.bitdefender.com https://posthog.com https://app.posthog.com https://us-assets.i.posthog.com https://*.teclara.tech; img-src 'self' data: https: http: https://posthog.com https://app.posthog.com https://us-assets.i.posthog.com https://*.teclara.tech; font-src 'self' https://fonts.gstatic.com; connect-src 'self' https://www.google-analytics.com https://region1.google-analytics.com https://analytics.google.com  https://umami.is https://cloud.umami.is https://api-gateway.umami.dev https://bitdefender.com https://*.bitdefender.com https://posthog.com https://app.posthog.com https://us-assets.i.posthog.com https://us.i.posthog.com https://*.teclara.tech; frame-src 'self' https://www.youtube.com https://www.google.com  https://umami.is https://bitdefender.com https://*.bitdefender.com https://posthog.com https://app.posthog.com https://*.teclara.tech; media-src 'self'; object-src 'none'; base-uri 'self'; form-action 'self'; frame-ancestors 'none'; upgrade-insecure-requests;",
      'X-Frame-Options': 'DENY',
      'X-Content-Type-Options': 'nosniff',
      'X-XSS-Protection': '1; mode=block',
      'Referrer-Policy': 'strict-origin-when-cross-origin',
      'Permissions-Policy': 'camera=(), microphone=(), geolocation=(), interest-cohort=()',
      'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload',
      'X-Permitted-Cross-Domain-Policies': 'none',
      'Clear-Site-Data': '"cache", "cookies", "storage"',
      'Cross-Origin-Embedder-Policy': 'credentialless',
      'Cross-Origin-Opener-Policy': 'same-origin',
      'Cross-Origin-Resource-Policy': 'same-site'
    }
  })
}
