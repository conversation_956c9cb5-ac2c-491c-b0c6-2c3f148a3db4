import React, { useState } from 'react';
import { FilloutSliderEmbed, FilloutPopupEmbed } from '@fillout/react';
import { Calendar, UserPlus } from 'lucide-react';
import { trackLeadGeneration, trackCTAClick } from '@/components/Analytics';
import { FormType, getFormConfig } from '@/utils/formConstants';

interface LeadGenerationButtonProps {
  formType: FormType;
  buttonText?: string;
  location?: string;
  className?: string;
  hideIcon?: boolean;
  utmParameters?: Record<string, string>;
  usePopup?: boolean;
}

const LeadGenerationButton: React.FC<LeadGenerationButtonProps> = ({
  formType,
  buttonText,
  location = 'Default Location',
  className = '',
  hideIcon = false,
  utmParameters = {},
  usePopup = false
}) => {
  const formConfig = getFormConfig(formType);
  const IconComponent = formConfig.icon === 'calendar' ? Calendar : UserPlus;

  const handleClick = () => {
    // Track as both CTA click and lead generation event
    trackCTAClick(buttonText || formConfig.defaultText, location);
    trackLeadGeneration(formType, location);
  };

  const defaultButtonClass =
    "relative inline-flex items-center justify-center gap-3 whitespace-nowrap rounded-full text-base font-bold ring-offset-background transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-gradient-to-r from-teclara-primary to-teclara-primary-hover text-white border-2 border-white/20 shadow-xl hover:shadow-[0_0_30px_rgba(255,25,25,0.5)] hover:scale-105 hover:border-white/40 active:scale-95 transform transition-all duration-300 group-hover:from-[#FF1919] group-hover:to-[#FF1919] group-hover:brightness-110 w-full sm:w-80 md:w-96 h-16 px-6";

  // Only apply essential layout/accessibility classes if custom className is provided
  const buttonClass = className && className.trim() !== ''
    ? `relative inline-flex items-center justify-center gap-3 whitespace-nowrap rounded-full text-base font-bold focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 ${className}`
    : defaultButtonClass;

  const [isOpen, setIsOpen] = useState(false);

  const handleButtonClick = () => {
    handleClick();
    setIsOpen(true);
  };

  return (
    <>
      <div className="relative group">
        {/* Enhanced glow effect - only show when using default styling */}
        {(!className || className.trim() === '') && (
          <div className="absolute -inset-1 bg-gradient-to-r from-teclara-primary to-teclara-primary-hover rounded-full blur opacity-20 group-hover:opacity-70 transition-all duration-300 group-hover:scale-105"></div>
        )}

        <button
          onClick={handleButtonClick}
          className={buttonClass}
        >
          {!hideIcon && <IconComponent className="w-5 h-5" />}
          <span className="relative text-center">
            {buttonText || formConfig.defaultText}
            {/* Underline effect - only show when using default styling */}
            {(!className || className.trim() === '') && (
              <span className="absolute -bottom-1 left-0 w-full h-0.5 bg-white/70 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300"></span>
            )}
          </span>
        </button>
      </div>

      {usePopup ? (
        <FilloutPopupEmbed
          filloutId={formConfig.id}
          inheritParameters
          parameters={{
            lead_type: formConfig.leadType,
            ...utmParameters,
          }}
          isOpen={isOpen}
          onClose={() => setIsOpen(false)}
        />
      ) : (
        <FilloutSliderEmbed
          filloutId={formConfig.id}
          inheritParameters
          parameters={{
            lead_type: formConfig.leadType,
            ...utmParameters,
          }}
          isOpen={isOpen}
          onClose={() => setIsOpen(false)}
          sliderDirection="right"
        />
      )}
    </>
  );
};

export default LeadGenerationButton; 