import { useEffect, useCallback } from 'react';
import posthog from 'posthog-js';

// TypeScript declarations for tracking scripts -11
declare global {
  interface Window {
    gtag: (
      command: string,
      action: string,
      params?: {
        event_category?: string;
        event_label?: string;
        value?: number;
        [key: string]: any;
      }
    ) => void;
    umami: {
      track: (eventName: string, eventData?: Record<string, any>) => void;
    };
    dataLayer: any[];
  }
}

// Define analytics event names as constants - Optimized for business value
const ANALYTICS_EVENTS = {
  // Core conversion events
  FORM_SUBMISSION: 'form_submission',
  LEAD_GENERATION: 'lead_generation',
  CONSULTATION_REQUEST: 'consultation_request',

  // Key user actions
  CTA_CLICK: 'cta_click',
  EXTERNAL_LINK_CLICK: 'external_link_click',
  FILE_DOWNLOAD: 'file_download',

  // Engagement milestones
  SCROLL_75: 'scroll_75_percent',

  // PostHog specific
  POSTHOG_PAGE_VIEW: '$pageview'
};

// Event tracking functions.
export const trackEvent = (
  eventName: string,
  eventData?: {
    category?: string;
    label?: string;
    value?: number;
    [key: string]: any;
  }
) => {
  // Google Analytics event tracking
  if (window.gtag) {
    window.gtag('event', eventName, {
      event_category: eventData?.category,
      event_label: eventData?.label,
      value: eventData?.value,
      ...eventData
    });
  }

  // Umami event tracking
  if (window.umami) {
    window.umami.track(eventName, eventData);
  }

  // PostHog event tracking
  if (posthog && typeof posthog.capture === 'function') {
    posthog.capture(eventName, eventData);
  }
};

// Lead generation tracking (high-value conversion event)
export const trackLeadGeneration = (formType: string, source: string) => {
  trackEvent(ANALYTICS_EVENTS.LEAD_GENERATION, {
    category: 'Conversion',
    label: formType,
    value: 1,
    form_type: formType,
    source: source
  });
};

// Form submission tracking (for all forms)
export const trackFormSubmission = (formName: string, success: boolean) => {
  trackEvent(ANALYTICS_EVENTS.FORM_SUBMISSION, {
    category: 'Form',
    label: formName,
    value: success ? 1 : 0,
    success,
    form_name: formName
  });
};

// CTA button click tracking (only for primary CTAs)
export const trackCTAClick = (ctaName: string, location: string) => {
  trackEvent(ANALYTICS_EVENTS.CTA_CLICK, {
    category: 'CTA',
    label: ctaName,
    cta_name: ctaName,
    location: location
  });
};

// Consultation request tracking (high-value event)
export const trackConsultationRequest = (serviceType: string, source: string) => {
  trackEvent(ANALYTICS_EVENTS.CONSULTATION_REQUEST, {
    category: 'Conversion',
    label: serviceType,
    value: 1,
    service_type: serviceType,
    source: source
  });
};

// File download tracking (for important resources)
export const trackFileDownload = (fileName: string, fileType: string) => {
  trackEvent(ANALYTICS_EVENTS.FILE_DOWNLOAD, {
    category: 'Download',
    label: fileName,
    file_name: fileName,
    file_type: fileType
  });
};

// External link click tracking (for partner/social links)
export const trackExternalLink = (linkUrl: string, linkText: string) => {
  trackEvent(ANALYTICS_EVENTS.EXTERNAL_LINK_CLICK, {
    category: 'Link',
    label: linkText,
    link_url: linkUrl,
    link_text: linkText
  });
};

// Custom hook for tracking user interactions
export const useAnalytics = () => {
  const trackPageView = useCallback((path: string) => {
    // Google Analytics page view
    if (window.gtag) {
      window.gtag('config', 'G-NLERJ6YWP4', {
        page_path: path,
        cookie_domain: 'auto',
      });
    }
    // Umami tracks page views automatically

    // PostHog page view tracking
    if (posthog && typeof posthog.capture === 'function') {
      posthog.capture(ANALYTICS_EVENTS.POSTHOG_PAGE_VIEW, { path });
    }
  }, []);

  return {
    trackEvent,
    trackLeadGeneration,
    trackFormSubmission,
    trackCTAClick,
    trackConsultationRequest,
    trackFileDownload,
    trackExternalLink,
    trackPageView
  };
};

// Throttle function for scroll events
const throttle = (func: (...args: any[]) => void, limit: number) => {
  let inThrottle: boolean;
  let lastFunc: ReturnType<typeof setTimeout>;
  let lastRan: number;
  return function(...args: any[]) {
    const context = this;
    if (!inThrottle) {
      func.apply(context, args);
      lastRan = Date.now();
      inThrottle = true;
    } else {
      clearTimeout(lastFunc);
      lastFunc = setTimeout(() => {
        if (Date.now() - lastRan >= limit) {
          func.apply(context, args);
          lastRan = Date.now();
        }
      }, limit - (Date.now() - lastRan));
    }
  };
};

const Analytics = () => {
  useEffect(() => {
    // Initialize Google Analytics
    const gaScript = document.createElement('script');
    gaScript.async = true;
    gaScript.src = `https://www.googletagmanager.com/gtag/js?id=G-NLERJ6YWP4`;
    document.head.appendChild(gaScript);

    window.dataLayer = window.dataLayer || [];
    function gtag(..._args: any[]) {
      window.dataLayer.push(arguments);
    }
    gtag('js', new Date());
    gtag('config', 'G-NLERJ6YWP4', {
      cookie_domain: 'auto',
    });

    // Initialize Umami
    const umamiScript = document.createElement('script');
    umamiScript.defer = true;
    umamiScript.src = "https://cloud.umami.is/script.js";
    umamiScript.setAttribute('data-website-id', 'ddc4d29c-df03-4c6b-84be-02fb79bbf76e');
    document.head.appendChild(umamiScript);

    // Add global click listener for external links only
    const handleLinkClick = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      const link = target.closest('a');
      if (link && link.hostname !== window.location.hostname) {
        const linkUrl = link.href;
        const linkText = link.textContent || 'Link';
        trackExternalLink(linkUrl, linkText);
      }
    };

    document.addEventListener('click', handleLinkClick);

    // Simplified scroll tracking - only 75% engagement milestone
    let scroll75Triggered = false;

    const handleScroll = throttle(() => {
      if (scroll75Triggered) return;

      const totalHeight = document.documentElement.scrollHeight - window.innerHeight;
      const scrolled = window.scrollY;
      const scrollPercentage = totalHeight > 0 ? Math.round((scrolled / totalHeight) * 100) : 0;

      if (scrollPercentage >= 75) {
        trackEvent(ANALYTICS_EVENTS.SCROLL_75, {
          category: 'Engagement',
          label: '75% Scroll',
          value: 75
        });
        scroll75Triggered = true;
      }
    }, 500); // Reduced frequency

    document.addEventListener('scroll', handleScroll);

    // Cleanup function
    return () => {
      document.head.removeChild(gaScript);
      document.head.removeChild(umamiScript);
      document.removeEventListener('click', handleLinkClick);
      document.removeEventListener('scroll', handleScroll);
    };
  }, []); 

  return null;
};

export default Analytics;