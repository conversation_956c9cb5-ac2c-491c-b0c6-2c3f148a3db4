/* Google Fonts import for new design system */
@import url("https://fonts.googleapis.com/css2?family=Outfit:wght@300;400;500;600;700;800&family=Roboto+Flex:opsz,wght@8..144,300;8..144,400;8..144,500;8..144,600;8..144,700;8..144,800&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

@keyframes heroLeft {
  0% {
    transform: translateY(0) scale(1);
  }
  50% {
    transform: translateY(-30px) scale(1.03);
  }
  100% {
    transform: translateY(0) scale(1);
  }
}
@keyframes heroRight {
  0% {
    transform: translateY(0) scale(1);
  }
  50% {
    transform: translateY(30px) scale(0.97);
  }
  100% {
    transform: translateY(0) scale(1);
  }
}
.animate-heroLeft {
  animation: heroLeft 8s ease-in-out infinite;
}
.animate-heroRight {
  animation: heroRight 8s ease-in-out infinite;
}

@layer base {
  :root {
    /* New Design System Variables - Updated Brief */
    --background-primary: #060d25;
    --background-secondary: #04081a;
    --background-tertiary: #060d25;
    --background-accent: #34c5b6;

    --text-primary: #ffffff;
    --text-secondary: #9ca3af;
    --text-tertiary: #6b7280;
    --text-inverse: #060d25;

    --brand-primary: #34c5b6;
    --brand-secondary: #fec400;
    --brand-accent: #34c5b6;
    --brand-muted: #5c5f63;

    --interactive-primary: #34c5b6;
    --interactive-primary-hover: #2ba89c;
    --interactive-secondary: #fec400;
    --interactive-secondary-hover: #e6b000;
    --interactive-muted: #374151;
    --interactive-muted-hover: #4b5563;

    --border-primary: #374151;
    --border-secondary: #4b5563;
    --border-accent: #34c5b6;

    --radius: 0.25rem; /* 4px border-radius as specified */
  }

  .dark {
    /* Dark mode theme variables - kept for compatibility */
    --background-primary: #060d25;
    --background-secondary: #04081a;
    --background-tertiary: #060d25;
    --background-accent: #1f2937;

    --text-primary: #ffffff;
    --text-secondary: #9ca3af;
    --text-tertiary: #6b7280;
    --text-inverse: #060d25;

    --brand-primary: #34c5b6;
    --brand-secondary: #fec400;
    --brand-accent: #34c5b6;
    --brand-muted: #5c5f63;

    --interactive-primary: #34c5b6;
    --interactive-primary-hover: #2ba89c;
    --interactive-secondary: #fec400;
    --interactive-secondary-hover: #e6b000;
    --interactive-muted: #374151;
    --interactive-muted-hover: #4b5563;

    --border-primary: #374151;
    --border-secondary: #4b5563;
    --border-accent: #34c5b6;
  }
}

@layer base {
  html {
    scroll-padding-top: 4rem;
  }

  body {
    @apply font-sans antialiased;
    font-family: "Roboto Flex", sans-serif;
    color: #ffffff;
    background-color: #060d25;
    font-size: 16px;
    line-height: 1.6;
    font-weight: 400;
  }

  /* Smooth scrolling only when user hasn't requested reduced motion */
  @media (prefers-reduced-motion: no-preference) {
    html {
      scroll-behavior: smooth;
    }

    /* Enable native momentum scrolling on iOS/Safari */
    body {
      -webkit-overflow-scrolling: touch;
    }
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-family: "Outfit", sans-serif;
    font-weight: 700;
    line-height: 1.2;
    color: #ffffff;
    letter-spacing: 0.02em;
  }

  h1 {
    font-size: 3rem; /* 48px for hero */
    font-weight: 800;
    letter-spacing: 0.02em;
  }

  h2 {
    font-size: 2rem; /* 32px for subheads */
    font-weight: 700;
  }

  h3 {
    font-size: 1.5rem; /* 24px for subheads */
    font-weight: 600;
  }

  h4 {
    font-size: 1.25rem;
    font-weight: 600;
  }

  p {
    font-size: 1rem; /* 16px base */
    line-height: 1.6;
    font-weight: 400;
    color: #ffffff;
  }

  @media (min-width: 640px) {
    body {
      font-size: 18px;
    }

    p {
      font-size: 1.125rem; /* 18px on larger screens */
    }
  }

  .section-padding {
    @apply py-20 md:py-32 px-4 md:px-8; /* ~120px padding on sections */
  }

  /* New Design System Button Styles - Updated Brief */
  .btn-primary {
    background-color: #34c5b6;
    color: white;
    padding: 0.75rem 2rem;
    border-radius: 0.25rem; /* 4px border-radius */
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 4px 14px 0 rgba(52, 197, 182, 0.25);
  }

  .btn-primary:hover {
    background-color: #2ba89c;
    box-shadow: 0 6px 20px 0 rgba(254, 196, 0, 0.6); /* Yellow glow on hover */
    transform: translateY(-2px);
  }

  .btn-secondary {
    background-color: transparent;
    color: #34c5b6;
    border: 2px solid #34c5b6;
    padding: 0.75rem 2rem;
    border-radius: 0.25rem; /* 4px border-radius */
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    transition: all 0.3s ease;
  }

  .btn-secondary:hover {
    background-color: transparent;
    color: #34c5b6;
    transform: translateY(-2px);
    box-shadow: 0 4px 14px 0 rgba(52, 197, 182, 0.4); /* Turquoise glow */
  }

  .btn-outline {
    background-color: transparent;
    color: #34c5b6;
    border: 2px solid #34c5b6;
    padding: 0.75rem 2rem;
    border-radius: 0.25rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    transition: all 0.3s ease;
  }

  .btn-outline:hover {
    background-color: #34c5b6;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 14px 0 rgba(52, 197, 182, 0.25);
  }

  /* Stats/Callout Highlights */
  .stat-highlight {
    background-color: #060d25;
    color: #34c5b6;
    font-weight: 700;
    font-size: 2rem;
    padding: 1rem;
    border-radius: 0.5rem;
  }

  /* Blockquotes with turquoise accent bar */
  blockquote {
    border-left: 4px solid #34c5b6;
    padding-left: 1.5rem;
    font-style: italic;
    color: #9ca3af;
  }

  .cyber-glow {
    box-shadow: 0 0 20px hsl(var(--cyber-glow)), 0 0 40px hsl(var(--cyber-glow)),
      0 0 60px hsl(var(--cyber-glow));
  }

  /* Background Glow Effects */
  .bg-gradient-radial {
    background: radial-gradient(circle at center, var(--tw-gradient-stops));
  }

  .bg-glow-turquoise {
    background: radial-gradient(
      circle at center,
      rgba(52, 197, 182, 0.15) 0%,
      rgba(52, 197, 182, 0.05) 50%,
      transparent 100%
    );
  }

  .bg-glow-yellow {
    background: radial-gradient(
      circle at center,
      rgba(254, 196, 0, 0.1) 0%,
      rgba(254, 196, 0, 0.03) 50%,
      transparent 100%
    );
  }

  .bg-glow-subtle {
    background: radial-gradient(
      ellipse at center,
      rgba(52, 197, 182, 0.08) 0%,
      rgba(52, 197, 182, 0.02) 40%,
      transparent 70%
    );
  }

  /* Animated glow orbs */
  .glow-orb {
    border-radius: 50%;
    filter: blur(40px);
    opacity: 0.6;
    animation: float 8s ease-in-out infinite;
  }

  .glow-orb-large {
    border-radius: 50%;
    filter: blur(60px);
    opacity: 0.4;
    animation: float 12s ease-in-out infinite reverse;
  }

  @keyframes float {
    0%,
    100% {
      transform: translateY(0px) scale(1);
    }
    50% {
      transform: translateY(-20px) scale(1.05);
    }
  }

  /* Subtle background grid with glow */
  .grid-glow {
    background-image: radial-gradient(
      circle at 1px 1px,
      rgba(52, 197, 182, 0.3) 1px,
      transparent 0
    );
    background-size: 50px 50px;
    opacity: 0.1;
  }

  /* Alternating section backgrounds with blended gradients */
  .section-bg-primary {
    background: linear-gradient(180deg, #060d25 0%, #071955 50%, #060d25 100%);
  }

  .section-bg-secondary {
    background: linear-gradient(180deg, #071955 0%, #060d25 50%, #071955 100%);
  }

  .section-bg-blend-to-primary {
    background: linear-gradient(180deg, #071955 0%, #060d25 100%);
  }

  .section-bg-blend-to-secondary {
    background: linear-gradient(180deg, #060d25 0%, #071955 100%);
  }
}

/* Accessibility Enhancements */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.focus\:not-sr-only:focus {
  position: static;
  width: auto;
  height: auto;
  padding: inherit;
  margin: inherit;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

/* High contrast mode */
.high-contrast {
  filter: contrast(150%) brightness(110%);
}

.high-contrast img,
.high-contrast video {
  filter: contrast(120%);
}

/* Font size adjustments */
[data-font-size="small"] {
  font-size: 0.875rem;
}

[data-font-size="large"] {
  font-size: 1.125rem;
}

[data-font-size="large"] h1 {
  font-size: 3.5rem;
}

[data-font-size="large"] h2 {
  font-size: 2.5rem;
}

[data-font-size="large"] h3 {
  font-size: 2rem;
}

/* Focus indicators */
[data-focus-visible="true"] {
  outline: 2px solid #d14019;
  outline-offset: 2px;
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  html,
  body {
    scroll-behavior: auto !important;
    -webkit-overflow-scrolling: auto !important;
  }
}

/* High contrast media query */
@media (prefers-contrast: high) {
  :root {
    --background: 0 0% 0%;
    --foreground: 0 0% 100%;
    --border: 0 0% 100%;
  }
}

/* Touch targets */
@media (pointer: coarse) {
  button,
  a,
  input,
  select,
  textarea {
    min-height: 44px;
    min-width: 44px;
  }
}
