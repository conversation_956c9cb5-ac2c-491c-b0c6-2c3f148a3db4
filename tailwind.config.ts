import type { Config } from "tailwindcss";
import tailwindcssAnimate from "tailwindcss-animate";
import { colors } from "./src/theme/colors";

export default {
  darkMode: "class",
  content: [
    "./pages/**/*.{ts,tsx}",
    "./components/**/*.{ts,tsx}",
    "./app/**/*.{ts,tsx}",
    "./src/**/*.{ts,tsx}",
  ],
  prefix: "",
  theme: {
    fontFamily: {
      sans: ["Robotoflex Variable", "Inter", "sans-serif"],
      heading: ["Outfit Variable", "Inter", "sans-serif"],
    },
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1280px",
      },
    },
    extend: {
      colors: {
        // CSS Custom Properties for dynamic theming
        border: "var(--border-primary)",
        input: "var(--background-secondary)",
        ring: "var(--brand-primary)",
        background: "var(--background-primary)",
        foreground: "var(--text-primary)",

        primary: {
          DEFAULT: "var(--brand-primary)",
          foreground: "var(--text-inverse)",
        },
        secondary: {
          DEFAULT: "var(--background-secondary)",
          foreground: "var(--text-primary)",
        },
        destructive: {
          DEFAULT: colors.semantic.error.light,
          foreground: "#FFFFFF",
        },
        muted: {
          DEFAULT: "var(--background-tertiary)",
          foreground: "var(--text-secondary)",
        },
        accent: {
          DEFAULT: "var(--brand-accent)",
          foreground: "var(--text-inverse)",
        },
        popover: {
          DEFAULT: "var(--background-primary)",
          foreground: "var(--text-primary)",
        },
        card: {
          DEFAULT: "var(--background-secondary)",
          foreground: "var(--text-primary)",
        },

        // New Design System Colors
        newDesign: {
          deepNavy: colors.brand.newDesign.deepNavy,
          turquoise: colors.brand.newDesign.turquoise,
          yellow: colors.brand.newDesign.yellow,
          darkNavy: colors.brand.newDesign.darkNavy,
          white: colors.brand.newDesign.white,
          lightGray: colors.brand.newDesign.lightGray,
        },

        // Brand Colors - Centralized from theme system
        teclara: {
          primary: colors.brand.primary.red,
          "primary-hover": colors.brand.primary.crimson,
          "primary-deep": colors.brand.primary.oxide,
          navy: colors.brand.primary.navy,
          "navy-light": colors.brand.extended.navyLight,
          "navy-dark": colors.brand.extended.navyDark,
          gunmetal: colors.brand.primary.gunmetal,
          "light-bg": colors.brand.background.light,
          "dark-bg": colors.brand.background.dark,
          white: colors.brand.extended.white,
          black: colors.brand.extended.black,
        },

        // Semantic Colors
        success: {
          DEFAULT: colors.semantic.success.light,
          dark: colors.semantic.success.dark,
          bg: colors.semantic.success.bg,
          text: colors.semantic.success.text,
        },
        warning: {
          DEFAULT: colors.semantic.warning.light,
          dark: colors.semantic.warning.dark,
          bg: colors.semantic.warning.bg,
          text: colors.semantic.warning.text,
        },
        error: {
          DEFAULT: colors.semantic.error.light,
          dark: colors.semantic.error.dark,
          bg: colors.semantic.error.bg,
          text: colors.semantic.error.text,
        },
        info: {
          DEFAULT: colors.semantic.info.light,
          dark: colors.semantic.info.dark,
          bg: colors.semantic.info.bg,
          text: colors.semantic.info.text,
        },

        // Contextual Colors - Threat levels
        threat: {
          critical: colors.contextual.threat.critical,
          high: colors.contextual.threat.high,
          medium: colors.contextual.threat.medium,
          low: colors.contextual.threat.low,
        },

        // Security states
        security: {
          secure: colors.contextual.security.secure,
          scanning: colors.contextual.security.scanning,
          vulnerable: colors.contextual.security.vulnerable,
          unknown: colors.contextual.security.unknown,
        },

        // Business states
        business: {
          active: colors.contextual.business.active,
          inactive: colors.contextual.business.inactive,
          pending: colors.contextual.business.pending,
          failed: colors.contextual.business.failed,
        },

        // Neutral grays
        gray: colors.neutral.gray,
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
        "pulse-subtle": {
          "0%, 100%": { transform: "scale(1)" },
          "50%": { transform: "scale(1.02)" },
        },
        "fade-in": {
          "0%": {
            opacity: "0",
            transform: "translateY(10px)",
          },
          "100%": {
            opacity: "1",
            transform: "translateY(0)",
          },
        },
        "fade-out": {
          "0%": {
            opacity: "1",
            transform: "translateY(0)",
          },
          "100%": {
            opacity: "0",
            transform: "translateY(10px)",
          },
        },
        "scale-in": {
          "0%": {
            transform: "scale(0.95)",
            opacity: "0",
          },
          "100%": {
            transform: "scale(1)",
            opacity: "1",
          },
        },
        float: {
          "0%, 100%": {
            transform: "translateY(0)",
          },
          "50%": {
            transform: "translateY(-10px)",
          },
        },
        "pulse-gentle": {
          "0%, 100%": {
            opacity: "1",
          },
          "50%": {
            opacity: "0.8",
          },
        },
        "bg-shimmer": {
          "0%": {
            backgroundPosition: "200% 0",
          },
          "100%": {
            backgroundPosition: "-200% 0",
          },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
        "fade-in": "fade-in 0.3s ease-out",
        "scale-in": "scale-in 0.2s ease-out",
        float: "float 6s ease-in-out infinite",
        "pulse-gentle": "pulse-gentle 3s ease-in-out infinite",
        "bg-shimmer": "bg-shimmer 8s infinite linear",
        "pulse-subtle": "pulse-subtle 2s cubic-bezier(0.4, 0, 0.6, 1) infinite",
      },
      backgroundImage: {
        "gradient-radial": "radial-gradient(var(--tw-gradient-stops))",
        "grid-white":
          'url("data:image/svg+xml,%3Csvg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"%3E%3Cpath d="M1 0V20M0 1H20" stroke="white" stroke-opacity="0.1"/%3E%3C/svg%3E")',
      },
    },
  },
  plugins: [tailwindcssAnimate],
} satisfies Config;
