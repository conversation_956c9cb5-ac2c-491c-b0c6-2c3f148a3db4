# Security Headers
/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: camera=(self), microphone=(self), geolocation=()
  Strict-Transport-Security: max-age=31536000; includeSubDomains; preload
  X-Permitted-Cross-Domain-Policies: none
  Cross-Origin-Opener-Policy: same-origin
  Cross-Origin-Resource-Policy: cross-origin
  Clear-Site-Data: "cache", "cookies", "storage", "executionContexts"
  Cross-Origin-Embedder-Policy: require-corp
  Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.google-analytics.com https://www.googletagmanager.com https://umami.is https://cloud.umami.is https://api-gateway.umami.dev https://bitdefender.com https://*.bitdefender.com https://static.cloudflareinsights.com https://posthog.com https://app.posthog.com https://us-assets.i.posthog.com https://us.i.posthog.com https://*.teclara.tech https://forms.fillout.com https://*.fillout.com data:; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://umami.is https://cloud.umami.is https://bitdefender.com https://*.bitdefender.com https://posthog.com https://app.posthog.com https://us-assets.i.posthog.com https://*.teclara.tech https://forms.fillout.com https://*.fillout.com; img-src 'self' data: https: http: https://posthog.com https://app.posthog.com https://us-assets.i.posthog.com https://*.teclara.tech; font-src 'self' https://fonts.gstatic.com https://forms.fillout.com https://*.fillout.com; connect-src 'self' https://www.google-analytics.com https://region1.google-analytics.com https://analytics.google.com https://umami.is https://cloud.umami.is https://api-gateway.umami.dev https://bitdefender.com https://*.bitdefender.com https://posthog.com https://app.posthog.com https://us-assets.i.posthog.com https://us.i.posthog.com https://*.teclara.tech https://forms.fillout.com https://*.fillout.com https://outlook.office.com; frame-src 'self' https://www.youtube.com https://www.google.com https://umami.is https://bitdefender.com https://*.bitdefender.com https://posthog.com https://app.posthog.com https://*.teclara.tech https://forms.fillout.com https://*.fillout.com https://outlook.office.com; media-src 'self'; object-src 'none'; base-uri 'self'; form-action 'self' https://forms.fillout.com https://*.fillout.com; frame-ancestors 'none'; upgrade-insecure-requests;
  X-Robots-Tag: index, follow
  X-DNS-Prefetch-Control: on
  Expect-CT: max-age=86400, enforce

# Cache control for static assets
/assets/*
  Cache-Control: public, max-age=31536000, immutable

# Cache control for HTML files
/*.html
  Cache-Control: public, max-age=0, must-revalidate

# Cache control for API responses
/api/*
  Cache-Control: no-cache, no-store, must-revalidate
  X-Content-Type-Options: nosniff
