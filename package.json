{"name": "vite_react_shadcn_ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "VITE_BUILD_NUMBER=$(git rev-parse --short HEAD) vite", "build": "vite build", "build:dev": "vite build --mode development", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@fillout/react": "^3.0.0", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@tanstack/react-query": "^5.17.19", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "framer-motion": "^12.18.1", "lucide-react": "^0.517.0", "next-themes": "^0.4.6", "posthog-js": "^1.255.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.4", "react-hook-form": "^7.58.1", "react-router-dom": "^6.30.1", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.67"}, "devDependencies": {"@eslint/js": "^9.29.0", "@tailwindcss/typography": "^0.5.10", "@types/node": "^20.19.1", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.17", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.5", "globals": "^16.2.0", "postcss": "^8.4.33", "rollup-plugin-visualizer": "^6.0.3", "tailwindcss": "^3.4.17", "terser": "^5.43.0", "typescript": "^5.3.3", "typescript-eslint": "^8.34.1", "vite": "^6.3.5", "vite-plugin-compression": "^0.5.1", "web-vitals": "^5.0.3"}, "engines": {"node": ">=18.0.0"}}